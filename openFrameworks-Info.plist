<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>${DEVELOPMENT_LANGUAGE}</string>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconFile</key>
	<string>${ICON_NAME}</string>
	<key>CFBundleIdentifier</key>
	<string>${PRODUCT_BUNDLE_IDENTIFIER}</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${TARGET_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleShortVersionString</key>
	<string>${MARKETING_VERSION}</string>
	<key>CFBundleVersion</key>
	<string>${CURRENT_PROJECT_VERSION}</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs to access the camera</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs to access the microphone</string>
	<key>NSHighResolutionCapable</key>
	<string>${HIGH_RESOLUTION_CAPABLE}</string>
	<key>NSCameraUseContinuityCameraDeviceType</key>
	<true/>
</dict>
</plist>
