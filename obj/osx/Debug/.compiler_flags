-g3 -DDEBUG -stdlib=libc++ -Wall -Werror=return-type -fexceptions -fpascal-strings -mmacosx-version-min=10.15 -std=c17 -fobjc-arc -DUSE_FMOD=0 -D__MACOSX_CORE__ -DGLM_FORCE_CTOR_INIT -DGLM_ENABLE_EXPERIMENTAL -mmacosx-version-min=10.15 -x objective-c++ -std=c++2b -I../../../libs/brotli/include -I../../../libs/brotli/include/brotli -I../../../libs/cairo/include -I../../../libs/cairo/include/cairo -I../../../libs/curl/include -I../../../libs/curl/include/curl -I../../../libs/fmt/include -I../../../libs/fmt/include/fmt -I../../../libs/FreeImage/include -I../../../libs/freetype/include -I../../../libs/freetype/include/freetype -I../../../libs/freetype/include/freetype/config -I../../../libs/freetype/include/freetype/internal -I../../../libs/freetype/include/freetype/internal/services -I../../../libs/glew/include -I../../../libs/glew/include/GL -I../../../libs/glfw/include -I../../../libs/glfw/include/GLFW -I../../../libs/glm/include -I../../../libs/glm/include/glm -I../../../libs/glm/include/glm/ext -I../../../libs/glm/include/glm/simd -I../../../libs/glm/include/glm/detail -I../../../libs/glm/include/glm/gtc -I../../../libs/glm/include/glm/gtx -I../../../libs/json/include -I../../../libs/json/include/nlohmann -I../../../libs/libpng/include -I../../../libs/libpng/include/libpng16 -I../../../libs/libusb/include -I../../../libs/openssl/include -I../../../libs/openssl/include/openssl -I../../../libs/pixman/include -I../../../libs/pugixml/include -I../../../libs/rtAudio/include -I../../../libs/tess2/include -I../../../libs/uriparser/include -I../../../libs/uriparser/include/uriparser -I../../../libs/utf8/include -I../../../libs/utf8/include/utf8 -I../../../libs/zlib/include -I../../../libs/openFrameworks -I../../../libs/openFrameworks/3d -I../../../libs/openFrameworks/video -I../../../libs/openFrameworks/types -I../../../libs/openFrameworks/app -I../../../libs/openFrameworks/utils -I../../../libs/openFrameworks/math -I../../../libs/openFrameworks/gl -I../../../libs/openFrameworks/communication -I../../../libs/openFrameworks/events -I../../../libs/openFrameworks/graphics -I../../../libs/openFrameworks/sound -I./src -I../../../addons/ofxFontStash3/src -I../../../addons/ofxFontStash3/libs -I../../../addons/ofxFontStash3/libs/fontstash -I../../../addons/ofxFontStash3/libs/fontstash/src -I../../../addons/ofxGPS/src -I../../../addons/ofxGPS/src/iOS -I../../../addons/ofxGPS/src/common -I../../../addons/ofxGPS/src/Android -stdlib=libc++ -mmacosx-version-min=10.15 -v -framework Accelerate -framework AGL -framework AppKit -framework ApplicationServices -framework AVFoundation -framework AudioToolbox -framework Cocoa -framework CoreVideo -framework CoreAudio -framework CoreMedia -framework CoreFoundation -framework CoreServices -framework Metal -framework Foundation -framework IOKit -framework OpenGL -framework QuartzCore -framework Security -framework SystemConfiguration
