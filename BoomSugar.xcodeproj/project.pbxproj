// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		288765FD0DF74451002DB57D /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 288765FC0DF74451002DB57D /* CoreGraphics.framework */; };
		5326AEA810A23A0500278DE6 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5326AEA710A23A0500278DE6 /* CoreLocation.framework */; };
		53F323EB10A20EDB00E0DAE4 /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 53F323EA10A20EDB00E0DAE4 /* OpenAL.framework */; };
		67DFA53619F92A69003B3434 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67DFA53419F92A5E003B3434 /* Accelerate.framework */; };
		901808C12053638E004A7774 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 901808BF2053636F004A7774 /* GLKit.framework */; };
		9936F6101BFA4DEE00891288 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9936F60F1BFA4DEE00891288 /* Images.xcassets */; };
		9936F6121BFA65F100891288 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9936F6111BFA65F100891288 /* LaunchScreen.storyboard */; };
		9969E7561C782C4500DEF0F6 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9969E7551C782C4500DEF0F6 /* CoreMotion.framework */; };
		BB16EBD20F2B2A9500518274 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BB16EBD10F2B2A9500518274 /* OpenGLES.framework */; };
		BB16EBD90F2B2AB500518274 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BB16EBD80F2B2AB500518274 /* QuartzCore.framework */; };
		BBE5EAB80F49AD8400F28951 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BBE5EAB70F49AD8400F28951 /* AudioToolbox.framework */; };
		"DA8215FD-BA3C-4DF2-8742-B11ADB454EC8" /* ofxGPSImpliOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = "F498E0D6-E16D-403D-90C6-3FC4D686D6B0" /* ofxGPSImpliOS.mm */; };
		E41D400B13B39D2100A75A5D /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E41D400613B39D2100A75A5D /* AVFoundation.framework */; };
		E41D400C13B39D2100A75A5D /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E41D400713B39D2100A75A5D /* CoreMedia.framework */; };
		E41D400D13B39D2100A75A5D /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E41D400813B39D2100A75A5D /* CoreVideo.framework */; };
		E41D400E13B39D2100A75A5D /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E41D400913B39D2100A75A5D /* MapKit.framework */; };
		E4D8936E11527B74007E1F53 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = E4D8936B11527B74007E1F53 /* main.mm */; };
		E4D8936F11527B74007E1F53 /* ofApp.mm in Sources */ = {isa = PBXBuildFile; fileRef = E4D8936D11527B74007E1F53 /* ofApp.mm */; };
		"FF105C75-6C47-490C-9193-B20D01890E2C" /* ofxFontStash.cpp in Sources */ = {isa = PBXBuildFile; fileRef = "680A5F11-5224-4B5F-A153-3166BD2C4652" /* ofxFontStash.cpp */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		"0803D5B6-60F5-442C-B14A-B1934A78B239" /* ofxGPS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = ofxGPS.h; sourceTree = "<group>"; };
		1D30AB110D05D00D00671497 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		1D6058910D05DD3D006BFB54 /* BoomSugar.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BoomSugar.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1DF5F4DF0D08C38300B7A737 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		"244DE5A2-3329-4DEB-B694-423374DA7864" /* stb_truetype.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = stb_truetype.h; sourceTree = "<group>"; };
		288765FC0DF74451002DB57D /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		"2C03B639-F30A-43ED-BC8F-C918DD9D4468" /* offontstash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = offontstash.h; sourceTree = "<group>"; };
		32CA4F630368D1EE00C91783 /* ofxiOS_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ofxiOS_Prefix.pch; sourceTree = "<group>"; };
		"392C1309-8963-4074-9E32-0694E8C191E2" /* data */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = folder; name = data; path = bin/data; sourceTree = SOURCE_ROOT; };
		"4CD3CD29-FCDA-46E8-BEA9-6E54801839C9" /* gl3corefontstash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = gl3corefontstash.h; sourceTree = "<group>"; };
		5326AEA710A23A0500278DE6 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		53F323EA10A20EDB00E0DAE4 /* OpenAL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenAL.framework; path = System/Library/Frameworks/OpenAL.framework; sourceTree = SDKROOT; };
		"5F9A742D-C0A9-491F-BA23-4449A1BA4200" /* murkafontstash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = murkafontstash.h; sourceTree = "<group>"; };
		67DFA53419F92A5E003B3434 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		"680A5F11-5224-4B5F-A153-3166BD2C4652" /* ofxFontStash.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ofxFontStash.cpp; sourceTree = "<group>"; };
		"7DFAE5B9-9864-4149-895B-F14160C1A657" /* ofxFontStash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = ofxFontStash.h; sourceTree = "<group>"; };
		901808BF2053636F004A7774 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		"98B44F2E-E00A-4D0B-A3A0-CF787831AB58" /* ofxGPSImpliOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = ofxGPSImpliOS.h; sourceTree = "<group>"; };
		9936F60F1BFA4DEE00891288 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		9936F6111BFA65F100891288 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		9969E7551C782C4500DEF0F6 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		BB16EBD10F2B2A9500518274 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		BB16EBD80F2B2AB500518274 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		BB24DDC910DA781C00E9C588 /* ofxiOS-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "ofxiOS-Info.plist"; sourceTree = "<group>"; };
		BBE5EAB70F49AD8400F28951 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		BF8CFDD22D756AC800AA5C47 /* ofMain.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ofMain.h; path = ../../../libs/openFrameworks/ofMain.h; sourceTree = "<group>"; };
		BF8CFDD32D756AC800AA5C47 /* ofMesh.inl */ = {isa = PBXFileReference; lastKnownFileType = text; path = ofMesh.inl; sourceTree = "<group>"; };
		BF8CFDD42D756AC800AA5C47 /* of3dPrimitives.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = of3dPrimitives.cpp; sourceTree = "<group>"; };
		BF8CFDD52D756AC800AA5C47 /* of3dPrimitives.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = of3dPrimitives.h; sourceTree = "<group>"; };
		BF8CFDD62D756AC800AA5C47 /* of3dUtils.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = of3dUtils.cpp; sourceTree = "<group>"; };
		BF8CFDD72D756AC800AA5C47 /* of3dUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = of3dUtils.h; sourceTree = "<group>"; };
		BF8CFDD82D756AC800AA5C47 /* ofCamera.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofCamera.cpp; sourceTree = "<group>"; };
		BF8CFDD92D756AC800AA5C47 /* ofCamera.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofCamera.h; sourceTree = "<group>"; };
		BF8CFDDA2D756AC800AA5C47 /* ofEasyCam.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofEasyCam.cpp; sourceTree = "<group>"; };
		BF8CFDDB2D756AC800AA5C47 /* ofEasyCam.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofEasyCam.h; sourceTree = "<group>"; };
		BF8CFDDC2D756AC800AA5C47 /* ofMesh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMesh.h; sourceTree = "<group>"; };
		BF8CFDDD2D756AC800AA5C47 /* ofNode.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofNode.cpp; sourceTree = "<group>"; };
		BF8CFDDE2D756AC800AA5C47 /* ofNode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofNode.h; sourceTree = "<group>"; };
		BF8CFDE02D756AC800AA5C47 /* ofBaseApp.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofBaseApp.cpp; sourceTree = "<group>"; };
		BF8CFDE12D756AC800AA5C47 /* ofAppBaseWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofAppBaseWindow.h; sourceTree = "<group>"; };
		BF8CFDE22D756AC800AA5C47 /* ofAppRunner.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofAppRunner.cpp; sourceTree = "<group>"; };
		BF8CFDE32D756AC800AA5C47 /* ofAppRunner.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofAppRunner.h; sourceTree = "<group>"; };
		BF8CFDE42D756AC800AA5C47 /* ofBaseApp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofBaseApp.h; sourceTree = "<group>"; };
		BF8CFDE52D756AC800AA5C47 /* ofMainLoop.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMainLoop.cpp; sourceTree = "<group>"; };
		BF8CFDE62D756AC800AA5C47 /* ofMainLoop.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMainLoop.h; sourceTree = "<group>"; };
		BF8CFDE72D756AC800AA5C47 /* ofWindowSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofWindowSettings.h; sourceTree = "<group>"; };
		BF8CFDEA2D756AC800AA5C47 /* ofEvent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofEvent.h; sourceTree = "<group>"; };
		BF8CFDEB2D756AC800AA5C47 /* ofEvents.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofEvents.cpp; sourceTree = "<group>"; };
		BF8CFDEC2D756AC800AA5C47 /* ofEvents.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofEvents.h; sourceTree = "<group>"; };
		BF8CFDED2D756AC800AA5C47 /* ofEventUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofEventUtils.h; sourceTree = "<group>"; };
		BF8CFDEF2D756AC800AA5C47 /* ofGLBaseTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGLBaseTypes.h; sourceTree = "<group>"; };
		BF8CFDF02D756AC800AA5C47 /* ofBufferObject.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofBufferObject.cpp; sourceTree = "<group>"; };
		BF8CFDF12D756AC800AA5C47 /* ofBufferObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofBufferObject.h; sourceTree = "<group>"; };
		BF8CFDF22D756AC800AA5C47 /* ofCubeMap.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofCubeMap.cpp; sourceTree = "<group>"; };
		BF8CFDF32D756AC800AA5C47 /* ofCubeMap.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofCubeMap.h; sourceTree = "<group>"; };
		BF8CFDF42D756AC800AA5C47 /* ofCubeMapShaders.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofCubeMapShaders.h; sourceTree = "<group>"; };
		BF8CFDF52D756AC800AA5C47 /* ofFbo.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofFbo.cpp; sourceTree = "<group>"; };
		BF8CFDF62D756AC800AA5C47 /* ofFbo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofFbo.h; sourceTree = "<group>"; };
		BF8CFDF72D756AC800AA5C47 /* ofGLProgrammableRenderer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofGLProgrammableRenderer.cpp; sourceTree = "<group>"; };
		BF8CFDF82D756AC800AA5C47 /* ofGLProgrammableRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGLProgrammableRenderer.h; sourceTree = "<group>"; };
		BF8CFDF92D756AC800AA5C47 /* ofGLRenderer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofGLRenderer.cpp; sourceTree = "<group>"; };
		BF8CFDFA2D756AC800AA5C47 /* ofGLRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGLRenderer.h; sourceTree = "<group>"; };
		BF8CFDFB2D756AC800AA5C47 /* ofGLUtils.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofGLUtils.cpp; sourceTree = "<group>"; };
		BF8CFDFC2D756AC800AA5C47 /* ofGLUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGLUtils.h; sourceTree = "<group>"; };
		BF8CFDFD2D756AC800AA5C47 /* ofLight.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofLight.cpp; sourceTree = "<group>"; };
		BF8CFDFE2D756AC800AA5C47 /* ofLight.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofLight.h; sourceTree = "<group>"; };
		BF8CFDFF2D756AC800AA5C47 /* ofShadow.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofShadow.cpp; sourceTree = "<group>"; };
		BF8CFE002D756AC800AA5C47 /* ofShadow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofShadow.h; sourceTree = "<group>"; };
		BF8CFE012D756AC800AA5C47 /* ofMaterial.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMaterial.cpp; sourceTree = "<group>"; };
		BF8CFE022D756AC800AA5C47 /* ofMaterial.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMaterial.h; sourceTree = "<group>"; };
		BF8CFE032D756AC800AA5C47 /* ofShader.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofShader.cpp; sourceTree = "<group>"; };
		BF8CFE042D756AC800AA5C47 /* ofShader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofShader.h; sourceTree = "<group>"; };
		BF8CFE052D756AC800AA5C47 /* ofTexture.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofTexture.cpp; sourceTree = "<group>"; };
		BF8CFE062D756AC800AA5C47 /* ofTexture.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTexture.h; sourceTree = "<group>"; };
		BF8CFE072D756AC800AA5C47 /* ofVbo.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVbo.cpp; sourceTree = "<group>"; };
		BF8CFE082D756AC800AA5C47 /* ofVbo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVbo.h; sourceTree = "<group>"; };
		BF8CFE092D756AC800AA5C47 /* ofVboMesh.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVboMesh.cpp; sourceTree = "<group>"; };
		BF8CFE0A2D756AC800AA5C47 /* ofVboMesh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVboMesh.h; sourceTree = "<group>"; };
		BF8CFE0C2D756AC800AA5C47 /* ofGraphicsBaseTypes.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofGraphicsBaseTypes.cpp; sourceTree = "<group>"; };
		BF8CFE0D2D756AC800AA5C47 /* ofGraphicsBaseTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGraphicsBaseTypes.h; sourceTree = "<group>"; };
		BF8CFE0E2D756AC800AA5C47 /* ofGraphicsConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGraphicsConstants.h; sourceTree = "<group>"; };
		BF8CFE0F2D756AC800AA5C47 /* ofPolyline.inl */ = {isa = PBXFileReference; lastKnownFileType = text; path = ofPolyline.inl; sourceTree = "<group>"; };
		BF8CFE102D756AC800AA5C47 /* of3dGraphics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = of3dGraphics.cpp; sourceTree = "<group>"; };
		BF8CFE112D756AC800AA5C47 /* of3dGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = of3dGraphics.h; sourceTree = "<group>"; };
		BF8CFE122D756AC800AA5C47 /* ofBitmapFont.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofBitmapFont.cpp; sourceTree = "<group>"; };
		BF8CFE132D756AC800AA5C47 /* ofBitmapFont.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofBitmapFont.h; sourceTree = "<group>"; };
		BF8CFE142D756AC800AA5C47 /* ofGraphics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofGraphics.cpp; sourceTree = "<group>"; };
		BF8CFE152D756AC800AA5C47 /* ofGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofGraphics.h; sourceTree = "<group>"; };
		BF8CFE162D756AC800AA5C47 /* ofImage.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofImage.cpp; sourceTree = "<group>"; };
		BF8CFE172D756AC800AA5C47 /* ofImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofImage.h; sourceTree = "<group>"; };
		BF8CFE182D756AC800AA5C47 /* ofPath.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofPath.cpp; sourceTree = "<group>"; };
		BF8CFE192D756AC800AA5C47 /* ofPath.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofPath.h; sourceTree = "<group>"; };
		BF8CFE1A2D756AC800AA5C47 /* ofPixels.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofPixels.cpp; sourceTree = "<group>"; };
		BF8CFE1B2D756AC800AA5C47 /* ofPixels.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofPixels.h; sourceTree = "<group>"; };
		BF8CFE1C2D756AC800AA5C47 /* ofPolyline.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofPolyline.h; sourceTree = "<group>"; };
		BF8CFE1D2D756AC800AA5C47 /* ofRendererCollection.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofRendererCollection.cpp; sourceTree = "<group>"; };
		BF8CFE1E2D756AC800AA5C47 /* ofRendererCollection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofRendererCollection.h; sourceTree = "<group>"; };
		BF8CFE1F2D756AC800AA5C47 /* ofTessellator.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofTessellator.cpp; sourceTree = "<group>"; };
		BF8CFE202D756AC800AA5C47 /* ofTessellator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTessellator.h; sourceTree = "<group>"; };
		BF8CFE212D756AC800AA5C47 /* ofTrueTypeFont.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofTrueTypeFont.cpp; sourceTree = "<group>"; };
		BF8CFE222D756AC800AA5C47 /* ofTrueTypeFont.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTrueTypeFont.h; sourceTree = "<group>"; };
		BF8CFE242D756AC800AA5C47 /* ofMathConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMathConstants.h; sourceTree = "<group>"; };
		BF8CFE252D756AC800AA5C47 /* ofMath.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMath.cpp; sourceTree = "<group>"; };
		BF8CFE262D756AC800AA5C47 /* ofMath.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMath.h; sourceTree = "<group>"; };
		BF8CFE272D756AC800AA5C47 /* ofMatrix3x3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMatrix3x3.cpp; sourceTree = "<group>"; };
		BF8CFE282D756AC800AA5C47 /* ofMatrix3x3.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMatrix3x3.h; sourceTree = "<group>"; };
		BF8CFE292D756AC800AA5C47 /* ofMatrix4x4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMatrix4x4.cpp; sourceTree = "<group>"; };
		BF8CFE2A2D756AC800AA5C47 /* ofMatrix4x4.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMatrix4x4.h; sourceTree = "<group>"; };
		BF8CFE2B2D756AC800AA5C47 /* ofQuaternion.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofQuaternion.cpp; sourceTree = "<group>"; };
		BF8CFE2C2D756AC800AA5C47 /* ofQuaternion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofQuaternion.h; sourceTree = "<group>"; };
		BF8CFE2D2D756AC800AA5C47 /* ofVec2f.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVec2f.cpp; sourceTree = "<group>"; };
		BF8CFE2E2D756AC800AA5C47 /* ofVec2f.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVec2f.h; sourceTree = "<group>"; };
		BF8CFE2F2D756AC800AA5C47 /* ofVec3f.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVec3f.h; sourceTree = "<group>"; };
		BF8CFE302D756AC800AA5C47 /* ofVec4f.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVec4f.cpp; sourceTree = "<group>"; };
		BF8CFE312D756AC800AA5C47 /* ofVec4f.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVec4f.h; sourceTree = "<group>"; };
		BF8CFE322D756AC800AA5C47 /* ofVectorMath.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVectorMath.h; sourceTree = "<group>"; };
		BF8CFE342D756AC800AA5C47 /* ofSoundBaseTypes.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofSoundBaseTypes.cpp; sourceTree = "<group>"; };
		BF8CFE352D756AC800AA5C47 /* ofSoundBaseTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSoundBaseTypes.h; sourceTree = "<group>"; };
		BF8CFE362D756AC800AA5C47 /* ofSoundBuffer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofSoundBuffer.cpp; sourceTree = "<group>"; };
		BF8CFE372D756AC800AA5C47 /* ofSoundBuffer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSoundBuffer.h; sourceTree = "<group>"; };
		BF8CFE382D756AC800AA5C47 /* ofSoundPlayer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofSoundPlayer.cpp; sourceTree = "<group>"; };
		BF8CFE392D756AC800AA5C47 /* ofSoundPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSoundPlayer.h; sourceTree = "<group>"; };
		BF8CFE3A2D756AC800AA5C47 /* ofSoundStream.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofSoundStream.cpp; sourceTree = "<group>"; };
		BF8CFE3B2D756AC800AA5C47 /* ofSoundStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSoundStream.h; sourceTree = "<group>"; };
		BF8CFE3C2D756AC800AA5C47 /* ofSoundUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSoundUtils.h; sourceTree = "<group>"; };
		BF8CFE3E2D756AC800AA5C47 /* ofBaseTypes.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofBaseTypes.cpp; sourceTree = "<group>"; };
		BF8CFE3F2D756AC800AA5C47 /* ofColor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofColor.cpp; sourceTree = "<group>"; };
		BF8CFE402D756AC800AA5C47 /* ofColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofColor.h; sourceTree = "<group>"; };
		BF8CFE412D756AC800AA5C47 /* ofParameter.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofParameter.cpp; sourceTree = "<group>"; };
		BF8CFE422D756AC800AA5C47 /* ofParameter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofParameter.h; sourceTree = "<group>"; };
		BF8CFE432D756AC800AA5C47 /* ofParameterGroup.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofParameterGroup.cpp; sourceTree = "<group>"; };
		BF8CFE442D756AC800AA5C47 /* ofParameterGroup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofParameterGroup.h; sourceTree = "<group>"; };
		BF8CFE452D756AC800AA5C47 /* ofPoint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofPoint.h; sourceTree = "<group>"; };
		BF8CFE462D756AC800AA5C47 /* ofRectangle.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofRectangle.cpp; sourceTree = "<group>"; };
		BF8CFE472D756AC800AA5C47 /* ofRectangle.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofRectangle.h; sourceTree = "<group>"; };
		BF8CFE482D756AC800AA5C47 /* ofTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTypes.h; sourceTree = "<group>"; };
		BF8CFE4A2D756AC800AA5C47 /* ofConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofConstants.h; sourceTree = "<group>"; };
		BF8CFE4B2D756AC800AA5C47 /* ofFileUtils.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofFileUtils.cpp; sourceTree = "<group>"; };
		BF8CFE4C2D756AC800AA5C47 /* ofFileUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofFileUtils.h; sourceTree = "<group>"; };
		BF8CFE4D2D756AC800AA5C47 /* ofFpsCounter.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofFpsCounter.cpp; sourceTree = "<group>"; };
		BF8CFE4E2D756AC800AA5C47 /* ofFpsCounter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofFpsCounter.h; sourceTree = "<group>"; };
		BF8CFE4F2D756AC800AA5C47 /* ofLog.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofLog.cpp; sourceTree = "<group>"; };
		BF8CFE502D756AC800AA5C47 /* ofLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofLog.h; sourceTree = "<group>"; };
		BF8CFE512D756AC800AA5C47 /* ofMatrixStack.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofMatrixStack.cpp; sourceTree = "<group>"; };
		BF8CFE522D756AC800AA5C47 /* ofMatrixStack.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofMatrixStack.h; sourceTree = "<group>"; };
		BF8CFE532D756AC800AA5C47 /* ofNoise.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofNoise.h; sourceTree = "<group>"; };
		BF8CFE542D756AC800AA5C47 /* ofSystemUtils.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofSystemUtils.cpp; sourceTree = "<group>"; };
		BF8CFE552D756AC800AA5C47 /* ofSystemUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofSystemUtils.h; sourceTree = "<group>"; };
		BF8CFE562D756AC800AA5C47 /* ofThread.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofThread.cpp; sourceTree = "<group>"; };
		BF8CFE572D756AC800AA5C47 /* ofThread.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofThread.h; sourceTree = "<group>"; };
		BF8CFE582D756AC800AA5C47 /* ofThreadChannel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofThreadChannel.h; sourceTree = "<group>"; };
		BF8CFE592D756AC800AA5C47 /* ofTimer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofTimer.cpp; sourceTree = "<group>"; };
		BF8CFE5A2D756AC800AA5C47 /* ofTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTimer.h; sourceTree = "<group>"; };
		BF8CFE5B2D756AC800AA5C47 /* ofTimerFps.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofTimerFps.cpp; sourceTree = "<group>"; };
		BF8CFE5C2D756AC800AA5C47 /* ofTimerFps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofTimerFps.h; sourceTree = "<group>"; };
		BF8CFE5D2D756AC800AA5C47 /* ofURLFileLoader.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofURLFileLoader.cpp; sourceTree = "<group>"; };
		BF8CFE5E2D756AC800AA5C47 /* ofURLFileLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofURLFileLoader.h; sourceTree = "<group>"; };
		BF8CFE5F2D756AC800AA5C47 /* ofUtils.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofUtils.cpp; sourceTree = "<group>"; };
		BF8CFE602D756AC800AA5C47 /* ofUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofUtils.h; sourceTree = "<group>"; };
		BF8CFE612D756AC800AA5C47 /* ofXml.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofXml.cpp; sourceTree = "<group>"; };
		BF8CFE622D756AC800AA5C47 /* ofXml.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofXml.h; sourceTree = "<group>"; };
		BF8CFE642D756AC800AA5C47 /* ofVideoGrabber.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVideoGrabber.cpp; sourceTree = "<group>"; };
		BF8CFE652D756AC800AA5C47 /* ofVideoGrabber.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVideoGrabber.h; sourceTree = "<group>"; };
		BF8CFE662D756AC800AA5C47 /* ofVideoPlayer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofVideoPlayer.cpp; sourceTree = "<group>"; };
		BF8CFE672D756AC800AA5C47 /* ofVideoPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofVideoPlayer.h; sourceTree = "<group>"; };
		BF8CFF542D758AD200AA5C47 /* ofAppiOSWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofAppiOSWindow.h; sourceTree = "<group>"; };
		BF8CFF552D758AD200AA5C47 /* ofAppiOSWindow.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofAppiOSWindow.mm; sourceTree = "<group>"; };
		BF8CFF562D758AD200AA5C47 /* ofxiOSApp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSApp.h; sourceTree = "<group>"; };
		BF8CFF582D758AD200AA5C47 /* ofxiOSAppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSAppDelegate.h; sourceTree = "<group>"; };
		BF8CFF592D758AD200AA5C47 /* ofxiOSAppDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSAppDelegate.mm; sourceTree = "<group>"; };
		BF8CFF5A2D758AD200AA5C47 /* ofxiOSEAGLView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSEAGLView.h; sourceTree = "<group>"; };
		BF8CFF5B2D758AD200AA5C47 /* ofxiOSEAGLView.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSEAGLView.mm; sourceTree = "<group>"; };
		BF8CFF5C2D758AD200AA5C47 /* ofxiOSGLKView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSGLKView.h; sourceTree = "<group>"; };
		BF8CFF5D2D758AD200AA5C47 /* ofxiOSGLKView.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSGLKView.mm; sourceTree = "<group>"; };
		BF8CFF5E2D758AD200AA5C47 /* ofxiOSGLKViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSGLKViewController.h; sourceTree = "<group>"; };
		BF8CFF5F2D758AD200AA5C47 /* ofxiOSGLKViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSGLKViewController.mm; sourceTree = "<group>"; };
		BF8CFF602D758AD200AA5C47 /* ofxiOSViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSViewController.h; sourceTree = "<group>"; };
		BF8CFF612D758AD200AA5C47 /* ofxiOSViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSViewController.mm; sourceTree = "<group>"; };
		BF8CFF632D758AD200AA5C47 /* ofxiOSAlerts.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSAlerts.h; sourceTree = "<group>"; };
		BF8CFF642D758AD200AA5C47 /* ofxiOSAlerts.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSAlerts.mm; sourceTree = "<group>"; };
		BF8CFF652D758AD200AA5C47 /* ofxiOSAlertsListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSAlertsListener.h; sourceTree = "<group>"; };
		BF8CFF672D758AD200AA5C47 /* EAGLKView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EAGLKView.h; sourceTree = "<group>"; };
		BF8CFF682D758AD200AA5C47 /* EAGLKView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EAGLKView.m; sourceTree = "<group>"; };
		BF8CFF692D758AD200AA5C47 /* EAGLView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EAGLView.h; sourceTree = "<group>"; };
		BF8CFF6A2D758AD200AA5C47 /* EAGLView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EAGLView.m; sourceTree = "<group>"; };
		BF8CFF6B2D758AD200AA5C47 /* ES1Renderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ES1Renderer.h; sourceTree = "<group>"; };
		BF8CFF6C2D758AD200AA5C47 /* ES1Renderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ES1Renderer.m; sourceTree = "<group>"; };
		BF8CFF6D2D758AD200AA5C47 /* ES2Renderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ES2Renderer.h; sourceTree = "<group>"; };
		BF8CFF6E2D758AD200AA5C47 /* ES2Renderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ES2Renderer.m; sourceTree = "<group>"; };
		BF8CFF6F2D758AD200AA5C47 /* ES3Renderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ES3Renderer.h; sourceTree = "<group>"; };
		BF8CFF702D758AD200AA5C47 /* ES3Renderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ES3Renderer.m; sourceTree = "<group>"; };
		BF8CFF712D758AD200AA5C47 /* ESRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ESRenderer.h; sourceTree = "<group>"; };
		BF8CFF732D758AD200AA5C47 /* AVSoundPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AVSoundPlayer.h; sourceTree = "<group>"; };
		BF8CFF742D758AD200AA5C47 /* AVSoundPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AVSoundPlayer.m; sourceTree = "<group>"; };
		BF8CFF752D758AD200AA5C47 /* ofxiOSSoundPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSSoundPlayer.h; sourceTree = "<group>"; };
		BF8CFF762D758AD200AA5C47 /* ofxiOSSoundPlayer.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSSoundPlayer.mm; sourceTree = "<group>"; };
		BF8CFF772D758AD200AA5C47 /* ofxiOSSoundStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSSoundStream.h; sourceTree = "<group>"; };
		BF8CFF782D758AD200AA5C47 /* ofxiOSSoundStream.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSSoundStream.mm; sourceTree = "<group>"; };
		BF8CFF792D758AD200AA5C47 /* ofxiOSSoundStreamDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSSoundStreamDelegate.h; sourceTree = "<group>"; };
		BF8CFF7A2D758AD200AA5C47 /* ofxiOSSoundStreamDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSSoundStreamDelegate.mm; sourceTree = "<group>"; };
		BF8CFF7B2D758AD200AA5C47 /* ofxOpenALSoundPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxOpenALSoundPlayer.h; sourceTree = "<group>"; };
		BF8CFF7C2D758AD200AA5C47 /* ofxOpenALSoundPlayer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofxOpenALSoundPlayer.cpp; sourceTree = "<group>"; };
		BF8CFF7D2D758AD200AA5C47 /* SoundEngine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SoundEngine.h; sourceTree = "<group>"; };
		BF8CFF7E2D758AD200AA5C47 /* SoundEngine.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = SoundEngine.cpp; sourceTree = "<group>"; };
		BF8CFF7F2D758AD200AA5C47 /* SoundInputStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SoundInputStream.h; sourceTree = "<group>"; };
		BF8CFF802D758AD200AA5C47 /* SoundInputStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SoundInputStream.m; sourceTree = "<group>"; };
		BF8CFF812D758AD200AA5C47 /* SoundOutputStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SoundOutputStream.h; sourceTree = "<group>"; };
		BF8CFF822D758AD200AA5C47 /* SoundOutputStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SoundOutputStream.m; sourceTree = "<group>"; };
		BF8CFF832D758AD200AA5C47 /* SoundStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SoundStream.h; sourceTree = "<group>"; };
		BF8CFF842D758AD200AA5C47 /* SoundStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SoundStream.m; sourceTree = "<group>"; };
		BF8CFF862D758AD200AA5C47 /* ofxtvOSAppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxtvOSAppDelegate.h; sourceTree = "<group>"; };
		BF8CFF872D758AD200AA5C47 /* ofxtvOSAppDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxtvOSAppDelegate.mm; sourceTree = "<group>"; };
		BF8CFF882D758AD200AA5C47 /* ofxtvOSGLKViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxtvOSGLKViewController.h; sourceTree = "<group>"; };
		BF8CFF892D758AD200AA5C47 /* ofxtvOSGLKViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxtvOSGLKViewController.mm; sourceTree = "<group>"; };
		BF8CFF8A2D758AD200AA5C47 /* ofxtvOSURLFileLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxtvOSURLFileLoader.h; sourceTree = "<group>"; };
		BF8CFF8B2D758AD200AA5C47 /* ofxtvOSURLFileLoader.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ofxtvOSURLFileLoader.cpp; sourceTree = "<group>"; };
		BF8CFF8C2D758AD200AA5C47 /* ofxtvOSViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxtvOSViewController.h; sourceTree = "<group>"; };
		BF8CFF8D2D758AD200AA5C47 /* ofxtvOSViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxtvOSViewController.mm; sourceTree = "<group>"; };
		BF8CFF8F2D758AD200AA5C47 /* ofxiOSCoreHaptics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSCoreHaptics.h; sourceTree = "<group>"; };
		BF8CFF902D758AD200AA5C47 /* ofxiOSCoreHaptics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSCoreHaptics.mm; sourceTree = "<group>"; };
		BF8CFF912D758AD200AA5C47 /* ofxiOSCoreLocation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSCoreLocation.h; sourceTree = "<group>"; };
		BF8CFF922D758AD200AA5C47 /* ofxiOSCoreLocation.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSCoreLocation.mm; sourceTree = "<group>"; };
		BF8CFF932D758AD200AA5C47 /* ofxiOSCoreMotion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSCoreMotion.h; sourceTree = "<group>"; };
		BF8CFF942D758AD200AA5C47 /* ofxiOSCoreMotion.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSCoreMotion.mm; sourceTree = "<group>"; };
		BF8CFF952D758AD200AA5C47 /* ofxiOSEventAdapter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSEventAdapter.h; sourceTree = "<group>"; };
		BF8CFF962D758AD200AA5C47 /* ofxiOSEventAdapter.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSEventAdapter.mm; sourceTree = "<group>"; };
		BF8CFF972D758AD200AA5C47 /* ofxiOSExternalDisplay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSExternalDisplay.h; sourceTree = "<group>"; };
		BF8CFF982D758AD200AA5C47 /* ofxiOSExternalDisplay.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSExternalDisplay.mm; sourceTree = "<group>"; };
		BF8CFF992D758AD200AA5C47 /* ofxiOSExtras.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSExtras.h; sourceTree = "<group>"; };
		BF8CFF9A2D758AD200AA5C47 /* ofxiOSExtras.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSExtras.mm; sourceTree = "<group>"; };
		BF8CFF9B2D758AD200AA5C47 /* ofxiOSImagePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSImagePicker.h; sourceTree = "<group>"; };
		BF8CFF9C2D758AD200AA5C47 /* ofxiOSImagePicker.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSImagePicker.mm; sourceTree = "<group>"; };
		BF8CFF9D2D758AD200AA5C47 /* ofxiOSKeyboard.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSKeyboard.h; sourceTree = "<group>"; };
		BF8CFF9E2D758AD200AA5C47 /* ofxiOSKeyboard.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSKeyboard.mm; sourceTree = "<group>"; };
		BF8CFF9F2D758AD200AA5C47 /* ofxiOSMapKit.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSMapKit.h; sourceTree = "<group>"; };
		BF8CFFA02D758AD200AA5C47 /* ofxiOSMapKit.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSMapKit.mm; sourceTree = "<group>"; };
		BF8CFFA12D758AD200AA5C47 /* ofxiOSMapKitDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSMapKitDelegate.h; sourceTree = "<group>"; };
		BF8CFFA22D758AD200AA5C47 /* ofxiOSMapKitDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSMapKitDelegate.mm; sourceTree = "<group>"; };
		BF8CFFA32D758AD200AA5C47 /* ofxiOSMapKitListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSMapKitListener.h; sourceTree = "<group>"; };
		BF8CFFA42D758AD200AA5C47 /* ofxiPhoneExtras.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiPhoneExtras.h; sourceTree = "<group>"; };
		BF8CFFA62D758AD200AA5C47 /* AVFoundationVideoGrabber.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AVFoundationVideoGrabber.h; sourceTree = "<group>"; };
		BF8CFFA72D758AD200AA5C47 /* AVFoundationVideoGrabber.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = AVFoundationVideoGrabber.mm; sourceTree = "<group>"; };
		BF8CFFA82D758AD200AA5C47 /* AVFoundationVideoPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AVFoundationVideoPlayer.h; sourceTree = "<group>"; };
		BF8CFFA92D758AD200AA5C47 /* AVFoundationVideoPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AVFoundationVideoPlayer.m; sourceTree = "<group>"; };
		BF8CFFAA2D758AD200AA5C47 /* ofxiOSVideoGrabber.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSVideoGrabber.h; sourceTree = "<group>"; };
		BF8CFFAB2D758AD200AA5C47 /* ofxiOSVideoGrabber.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSVideoGrabber.mm; sourceTree = "<group>"; };
		BF8CFFAC2D758AD200AA5C47 /* ofxiOSVideoPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSVideoPlayer.h; sourceTree = "<group>"; };
		BF8CFFAD2D758AD200AA5C47 /* ofxiOSVideoPlayer.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxiOSVideoPlayer.mm; sourceTree = "<group>"; };
		BF8CFFAF2D758AD200AA5C47 /* ofxiOS.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOS.h; sourceTree = "<group>"; };
		BF8CFFB02D758AD200AA5C47 /* ofxiOSConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSConstants.h; sourceTree = "<group>"; };
		BF8CFFB12D758AD200AA5C47 /* ofxiOSExtensions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiOSExtensions.h; sourceTree = "<group>"; };
		BF8CFFB22D758AD200AA5C47 /* ofxiPhone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxiPhone.h; sourceTree = "<group>"; };
		BF8CFFB32D758AD200AA5C47 /* ofxtvOS.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ofxtvOS.h; sourceTree = "<group>"; };
		"CA6F54CF-7CC2-4452-94AF-A650E61221C6" /* glfontstash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = glfontstash.h; sourceTree = "<group>"; };
		E41D3ED613B38FB500A75A5D /* Project.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = Project.xcconfig; sourceTree = "<group>"; };
		E41D3EE513B3906D00A75A5D /* CoreOF.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = CoreOF.xcconfig; path = ../../../libs/openFrameworksCompiled/project/ios/CoreOF.xcconfig; sourceTree = SOURCE_ROOT; };
		E41D400613B39D2100A75A5D /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		E41D400713B39D2100A75A5D /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		E41D400813B39D2100A75A5D /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		E41D400913B39D2100A75A5D /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		E4A823A312561BE3002F86A2 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		E4D8936B11527B74007E1F53 /* main.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = main.mm; path = src/main.mm; sourceTree = SOURCE_ROOT; };
		E4D8936C11527B74007E1F53 /* ofApp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ofApp.h; path = src/ofApp.h; sourceTree = SOURCE_ROOT; };
		E4D8936D11527B74007E1F53 /* ofApp.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = ofApp.mm; path = src/ofApp.mm; sourceTree = SOURCE_ROOT; };
		"F1773221-5B8F-4940-8869-E9B64DD78763" /* fontstash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = fontstash.h; sourceTree = "<group>"; };
		"F498E0D6-E16D-403D-90C6-3FC4D686D6B0" /* ofxGPSImpliOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ofxGPSImpliOS.mm; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D60588F0D05DD3D006BFB54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9969E7561C782C4500DEF0F6 /* CoreMotion.framework in Frameworks */,
				901808C12053638E004A7774 /* GLKit.framework in Frameworks */,
				1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */,
				1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */,
				288765FD0DF74451002DB57D /* CoreGraphics.framework in Frameworks */,
				BB16EBD20F2B2A9500518274 /* OpenGLES.framework in Frameworks */,
				BB16EBD90F2B2AB500518274 /* QuartzCore.framework in Frameworks */,
				BBE5EAB80F49AD8400F28951 /* AudioToolbox.framework in Frameworks */,
				53F323EB10A20EDB00E0DAE4 /* OpenAL.framework in Frameworks */,
				5326AEA810A23A0500278DE6 /* CoreLocation.framework in Frameworks */,
				E41D400B13B39D2100A75A5D /* AVFoundation.framework in Frameworks */,
				E41D400C13B39D2100A75A5D /* CoreMedia.framework in Frameworks */,
				E41D400D13B39D2100A75A5D /* CoreVideo.framework in Frameworks */,
				E41D400E13B39D2100A75A5D /* MapKit.framework in Frameworks */,
				67DFA53619F92A69003B3434 /* Accelerate.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				1D6058910D05DD3D006BFB54 /* BoomSugar.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		"1FB7C63F-1058-4B5D-B654-8782AB29F8B3" /* src */ = {
			isa = PBXGroup;
			children = (
				"F1773221-5B8F-4940-8869-E9B64DD78763" /* fontstash.h */,
				"4CD3CD29-FCDA-46E8-BEA9-6E54801839C9" /* gl3corefontstash.h */,
				"CA6F54CF-7CC2-4452-94AF-A650E61221C6" /* glfontstash.h */,
				"5F9A742D-C0A9-491F-BA23-4449A1BA4200" /* murkafontstash.h */,
				"2C03B639-F30A-43ED-BC8F-C918DD9D4468" /* offontstash.h */,
				"244DE5A2-3329-4DEB-B694-423374DA7864" /* stb_truetype.h */,
			);
			path = src;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* CustomTemplate */ = {
			isa = PBXGroup;
			children = (
				9936F60E1BFA4DEE00891288 /* mediaAssets */,
				E4D8936A11527B74007E1F53 /* src */,
				BB24E1F710DAA51900E9C588 /* openFrameworks */,
				BB16F26B0F2B646B00518274 /* addons */,
				BB16E9930F2B1E5900518274 /* libs */,
				19C28FACFE9D520D11CA2CBB /* Products */,
				901808C02053638E004A7774 /* Frameworks */,
				"392C1309-8963-4074-9E32-0694E8C191E2" /* data */,
			);
			name = CustomTemplate;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* core frameworks */ = {
			isa = PBXGroup;
			children = (
				67DFA53419F92A5E003B3434 /* Accelerate.framework */,
				901808BF2053636F004A7774 /* GLKit.framework */,
				BBE5EAB70F49AD8400F28951 /* AudioToolbox.framework */,
				E41D400613B39D2100A75A5D /* AVFoundation.framework */,
				E4A823A312561BE3002F86A2 /* CoreGraphics.framework */,
				5326AEA710A23A0500278DE6 /* CoreLocation.framework */,
				E41D400713B39D2100A75A5D /* CoreMedia.framework */,
				9969E7551C782C4500DEF0F6 /* CoreMotion.framework */,
				E41D400813B39D2100A75A5D /* CoreVideo.framework */,
				1D30AB110D05D00D00671497 /* Foundation.framework */,
				E41D400913B39D2100A75A5D /* MapKit.framework */,
				53F323EA10A20EDB00E0DAE4 /* OpenAL.framework */,
				BB16EBD10F2B2A9500518274 /* OpenGLES.framework */,
				BB16EBD80F2B2AB500518274 /* QuartzCore.framework */,
				1DF5F4DF0D08C38300B7A737 /* UIKit.framework */,
				288765FC0DF74451002DB57D /* CoreGraphics.framework */,
			);
			name = "core frameworks";
			sourceTree = "<group>";
		};
		"33E4F14B-CDF4-4E0F-8F2D-015FC471AA56" /* src */ = {
			isa = PBXGroup;
			children = (
				"680A5F11-5224-4B5F-A153-3166BD2C4652" /* ofxFontStash.cpp */,
				"7DFAE5B9-9864-4149-895B-F14160C1A657" /* ofxFontStash.h */,
			);
			path = src;
			sourceTree = "<group>";
		};
		"5AD0F617-D426-4BF8-AC01-27EF54A7FFE8" /* ofxFontStash3 */ = {
			isa = PBXGroup;
			children = (
				"C1FDFD75-7745-48E9-9247-8E50161375B0" /* libs */,
				"33E4F14B-CDF4-4E0F-8F2D-015FC471AA56" /* src */,
			);
			path = ofxFontStash3;
			sourceTree = "<group>";
		};
		6948EE371B920CB800B5AC1A /* local_addons */ = {
			isa = PBXGroup;
			children = (
			);
			name = local_addons;
			sourceTree = "<group>";
		};
		901808C02053638E004A7774 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9936F60E1BFA4DEE00891288 /* mediaAssets */ = {
			isa = PBXGroup;
			children = (
				9936F6111BFA65F100891288 /* LaunchScreen.storyboard */,
				9936F60F1BFA4DEE00891288 /* Images.xcassets */,
			);
			path = mediaAssets;
			sourceTree = "<group>";
		};
		"A0EB2D23-921A-4DDD-9D79-6D5F14FBD4A2" /* ofxGPS */ = {
			isa = PBXGroup;
			children = (
				"D2BD6EE2-4867-42F4-83A2-E9F25D2A83A3" /* src */,
			);
			path = ofxGPS;
			sourceTree = "<group>";
		};
		"A5547EE2-32EA-4E4D-901F-743837DA224A" /* common */ = {
			isa = PBXGroup;
			children = (
				"0803D5B6-60F5-442C-B14A-B1934A78B239" /* ofxGPS.h */,
			);
			path = common;
			sourceTree = "<group>";
		};
		BB16E9930F2B1E5900518274 /* libs */ = {
			isa = PBXGroup;
			children = (
				BBE5E94E0F497BD800F28951 /* core */,
			);
			name = libs;
			sourceTree = "<group>";
		};
		BB16F26B0F2B646B00518274 /* addons */ = {
			isa = PBXGroup;
			children = (
				"5AD0F617-D426-4BF8-AC01-27EF54A7FFE8" /* ofxFontStash3 */,
				"A0EB2D23-921A-4DDD-9D79-6D5F14FBD4A2" /* ofxGPS */,
			);
			name = addons;
			path = ../../../addons;
			sourceTree = SOURCE_ROOT;
		};
		BB24E1F710DAA51900E9C588 /* openFrameworks */ = {
			isa = PBXGroup;
			children = (
				BF8CFFB52D758AD200AA5C47 /* ofxiOS */,
				BF8CFDD22D756AC800AA5C47 /* ofMain.h */,
				BF8CFDDF2D756AC800AA5C47 /* 3d */,
				BF8CFDE82D756AC800AA5C47 /* app */,
				BF8CFDE92D756AC800AA5C47 /* communication */,
				BF8CFDEE2D756AC800AA5C47 /* events */,
				BF8CFE0B2D756AC800AA5C47 /* gl */,
				BF8CFE232D756AC800AA5C47 /* graphics */,
				BF8CFE332D756AC800AA5C47 /* math */,
				BF8CFE3D2D756AC800AA5C47 /* sound */,
				BF8CFE492D756AC800AA5C47 /* types */,
				BF8CFE632D756AC800AA5C47 /* utils */,
				BF8CFE682D756AC800AA5C47 /* video */,
				32CA4F630368D1EE00C91783 /* ofxiOS_Prefix.pch */,
				BB24DDC910DA781C00E9C588 /* ofxiOS-Info.plist */,
				E41D3EE513B3906D00A75A5D /* CoreOF.xcconfig */,
				6948EE371B920CB800B5AC1A /* local_addons */,
				E41D3ED613B38FB500A75A5D /* Project.xcconfig */,
			);
			name = openFrameworks;
			sourceTree = "<group>";
		};
		BBE5E94E0F497BD800F28951 /* core */ = {
			isa = PBXGroup;
			children = (
				29B97323FDCFA39411CA2CEA /* core frameworks */,
			);
			name = core;
			sourceTree = "<group>";
		};
		BF8CFDDF2D756AC800AA5C47 /* 3d */ = {
			isa = PBXGroup;
			children = (
				BF8CFDD32D756AC800AA5C47 /* ofMesh.inl */,
				BF8CFDD42D756AC800AA5C47 /* of3dPrimitives.cpp */,
				BF8CFDD52D756AC800AA5C47 /* of3dPrimitives.h */,
				BF8CFDD62D756AC800AA5C47 /* of3dUtils.cpp */,
				BF8CFDD72D756AC800AA5C47 /* of3dUtils.h */,
				BF8CFDD82D756AC800AA5C47 /* ofCamera.cpp */,
				BF8CFDD92D756AC800AA5C47 /* ofCamera.h */,
				BF8CFDDA2D756AC800AA5C47 /* ofEasyCam.cpp */,
				BF8CFDDB2D756AC800AA5C47 /* ofEasyCam.h */,
				BF8CFDDC2D756AC800AA5C47 /* ofMesh.h */,
				BF8CFDDD2D756AC800AA5C47 /* ofNode.cpp */,
				BF8CFDDE2D756AC800AA5C47 /* ofNode.h */,
			);
			name = 3d;
			path = ../../../libs/openFrameworks/3d;
			sourceTree = "<group>";
		};
		BF8CFDE82D756AC800AA5C47 /* app */ = {
			isa = PBXGroup;
			children = (
				BF8CFDE02D756AC800AA5C47 /* ofBaseApp.cpp */,
				BF8CFDE12D756AC800AA5C47 /* ofAppBaseWindow.h */,
				BF8CFDE22D756AC800AA5C47 /* ofAppRunner.cpp */,
				BF8CFDE32D756AC800AA5C47 /* ofAppRunner.h */,
				BF8CFDE42D756AC800AA5C47 /* ofBaseApp.h */,
				BF8CFDE52D756AC800AA5C47 /* ofMainLoop.cpp */,
				BF8CFDE62D756AC800AA5C47 /* ofMainLoop.h */,
				BF8CFDE72D756AC800AA5C47 /* ofWindowSettings.h */,
			);
			name = app;
			path = ../../../libs/openFrameworks/app;
			sourceTree = "<group>";
		};
		BF8CFDE92D756AC800AA5C47 /* communication */ = {
			isa = PBXGroup;
			children = (
			);
			name = communication;
			path = ../../../libs/openFrameworks/communication;
			sourceTree = "<group>";
		};
		BF8CFDEE2D756AC800AA5C47 /* events */ = {
			isa = PBXGroup;
			children = (
				BF8CFDEA2D756AC800AA5C47 /* ofEvent.h */,
				BF8CFDEB2D756AC800AA5C47 /* ofEvents.cpp */,
				BF8CFDEC2D756AC800AA5C47 /* ofEvents.h */,
				BF8CFDED2D756AC800AA5C47 /* ofEventUtils.h */,
			);
			name = events;
			path = ../../../libs/openFrameworks/events;
			sourceTree = "<group>";
		};
		BF8CFE0B2D756AC800AA5C47 /* gl */ = {
			isa = PBXGroup;
			children = (
				BF8CFDEF2D756AC800AA5C47 /* ofGLBaseTypes.h */,
				BF8CFDF02D756AC800AA5C47 /* ofBufferObject.cpp */,
				BF8CFDF12D756AC800AA5C47 /* ofBufferObject.h */,
				BF8CFDF22D756AC800AA5C47 /* ofCubeMap.cpp */,
				BF8CFDF32D756AC800AA5C47 /* ofCubeMap.h */,
				BF8CFDF42D756AC800AA5C47 /* ofCubeMapShaders.h */,
				BF8CFDF52D756AC800AA5C47 /* ofFbo.cpp */,
				BF8CFDF62D756AC800AA5C47 /* ofFbo.h */,
				BF8CFDF72D756AC800AA5C47 /* ofGLProgrammableRenderer.cpp */,
				BF8CFDF82D756AC800AA5C47 /* ofGLProgrammableRenderer.h */,
				BF8CFDF92D756AC800AA5C47 /* ofGLRenderer.cpp */,
				BF8CFDFA2D756AC800AA5C47 /* ofGLRenderer.h */,
				BF8CFDFB2D756AC800AA5C47 /* ofGLUtils.cpp */,
				BF8CFDFC2D756AC800AA5C47 /* ofGLUtils.h */,
				BF8CFDFD2D756AC800AA5C47 /* ofLight.cpp */,
				BF8CFDFE2D756AC800AA5C47 /* ofLight.h */,
				BF8CFDFF2D756AC800AA5C47 /* ofShadow.cpp */,
				BF8CFE002D756AC800AA5C47 /* ofShadow.h */,
				BF8CFE012D756AC800AA5C47 /* ofMaterial.cpp */,
				BF8CFE022D756AC800AA5C47 /* ofMaterial.h */,
				BF8CFE032D756AC800AA5C47 /* ofShader.cpp */,
				BF8CFE042D756AC800AA5C47 /* ofShader.h */,
				BF8CFE052D756AC800AA5C47 /* ofTexture.cpp */,
				BF8CFE062D756AC800AA5C47 /* ofTexture.h */,
				BF8CFE072D756AC800AA5C47 /* ofVbo.cpp */,
				BF8CFE082D756AC800AA5C47 /* ofVbo.h */,
				BF8CFE092D756AC800AA5C47 /* ofVboMesh.cpp */,
				BF8CFE0A2D756AC800AA5C47 /* ofVboMesh.h */,
			);
			name = gl;
			path = ../../../libs/openFrameworks/gl;
			sourceTree = "<group>";
		};
		BF8CFE232D756AC800AA5C47 /* graphics */ = {
			isa = PBXGroup;
			children = (
				BF8CFE0C2D756AC800AA5C47 /* ofGraphicsBaseTypes.cpp */,
				BF8CFE0D2D756AC800AA5C47 /* ofGraphicsBaseTypes.h */,
				BF8CFE0E2D756AC800AA5C47 /* ofGraphicsConstants.h */,
				BF8CFE0F2D756AC800AA5C47 /* ofPolyline.inl */,
				BF8CFE102D756AC800AA5C47 /* of3dGraphics.cpp */,
				BF8CFE112D756AC800AA5C47 /* of3dGraphics.h */,
				BF8CFE122D756AC800AA5C47 /* ofBitmapFont.cpp */,
				BF8CFE132D756AC800AA5C47 /* ofBitmapFont.h */,
				BF8CFE142D756AC800AA5C47 /* ofGraphics.cpp */,
				BF8CFE152D756AC800AA5C47 /* ofGraphics.h */,
				BF8CFE162D756AC800AA5C47 /* ofImage.cpp */,
				BF8CFE172D756AC800AA5C47 /* ofImage.h */,
				BF8CFE182D756AC800AA5C47 /* ofPath.cpp */,
				BF8CFE192D756AC800AA5C47 /* ofPath.h */,
				BF8CFE1A2D756AC800AA5C47 /* ofPixels.cpp */,
				BF8CFE1B2D756AC800AA5C47 /* ofPixels.h */,
				BF8CFE1C2D756AC800AA5C47 /* ofPolyline.h */,
				BF8CFE1D2D756AC800AA5C47 /* ofRendererCollection.cpp */,
				BF8CFE1E2D756AC800AA5C47 /* ofRendererCollection.h */,
				BF8CFE1F2D756AC800AA5C47 /* ofTessellator.cpp */,
				BF8CFE202D756AC800AA5C47 /* ofTessellator.h */,
				BF8CFE212D756AC800AA5C47 /* ofTrueTypeFont.cpp */,
				BF8CFE222D756AC800AA5C47 /* ofTrueTypeFont.h */,
			);
			name = graphics;
			path = ../../../libs/openFrameworks/graphics;
			sourceTree = "<group>";
		};
		BF8CFE332D756AC800AA5C47 /* math */ = {
			isa = PBXGroup;
			children = (
				BF8CFE242D756AC800AA5C47 /* ofMathConstants.h */,
				BF8CFE252D756AC800AA5C47 /* ofMath.cpp */,
				BF8CFE262D756AC800AA5C47 /* ofMath.h */,
				BF8CFE272D756AC800AA5C47 /* ofMatrix3x3.cpp */,
				BF8CFE282D756AC800AA5C47 /* ofMatrix3x3.h */,
				BF8CFE292D756AC800AA5C47 /* ofMatrix4x4.cpp */,
				BF8CFE2A2D756AC800AA5C47 /* ofMatrix4x4.h */,
				BF8CFE2B2D756AC800AA5C47 /* ofQuaternion.cpp */,
				BF8CFE2C2D756AC800AA5C47 /* ofQuaternion.h */,
				BF8CFE2D2D756AC800AA5C47 /* ofVec2f.cpp */,
				BF8CFE2E2D756AC800AA5C47 /* ofVec2f.h */,
				BF8CFE2F2D756AC800AA5C47 /* ofVec3f.h */,
				BF8CFE302D756AC800AA5C47 /* ofVec4f.cpp */,
				BF8CFE312D756AC800AA5C47 /* ofVec4f.h */,
				BF8CFE322D756AC800AA5C47 /* ofVectorMath.h */,
			);
			name = math;
			path = ../../../libs/openFrameworks/math;
			sourceTree = "<group>";
		};
		BF8CFE3D2D756AC800AA5C47 /* sound */ = {
			isa = PBXGroup;
			children = (
				BF8CFE342D756AC800AA5C47 /* ofSoundBaseTypes.cpp */,
				BF8CFE352D756AC800AA5C47 /* ofSoundBaseTypes.h */,
				BF8CFE362D756AC800AA5C47 /* ofSoundBuffer.cpp */,
				BF8CFE372D756AC800AA5C47 /* ofSoundBuffer.h */,
				BF8CFE382D756AC800AA5C47 /* ofSoundPlayer.cpp */,
				BF8CFE392D756AC800AA5C47 /* ofSoundPlayer.h */,
				BF8CFE3A2D756AC800AA5C47 /* ofSoundStream.cpp */,
				BF8CFE3B2D756AC800AA5C47 /* ofSoundStream.h */,
				BF8CFE3C2D756AC800AA5C47 /* ofSoundUtils.h */,
			);
			name = sound;
			path = ../../../libs/openFrameworks/sound;
			sourceTree = "<group>";
		};
		BF8CFE492D756AC800AA5C47 /* types */ = {
			isa = PBXGroup;
			children = (
				BF8CFE3E2D756AC800AA5C47 /* ofBaseTypes.cpp */,
				BF8CFE3F2D756AC800AA5C47 /* ofColor.cpp */,
				BF8CFE402D756AC800AA5C47 /* ofColor.h */,
				BF8CFE412D756AC800AA5C47 /* ofParameter.cpp */,
				BF8CFE422D756AC800AA5C47 /* ofParameter.h */,
				BF8CFE432D756AC800AA5C47 /* ofParameterGroup.cpp */,
				BF8CFE442D756AC800AA5C47 /* ofParameterGroup.h */,
				BF8CFE452D756AC800AA5C47 /* ofPoint.h */,
				BF8CFE462D756AC800AA5C47 /* ofRectangle.cpp */,
				BF8CFE472D756AC800AA5C47 /* ofRectangle.h */,
				BF8CFE482D756AC800AA5C47 /* ofTypes.h */,
			);
			name = types;
			path = ../../../libs/openFrameworks/types;
			sourceTree = "<group>";
		};
		BF8CFE632D756AC800AA5C47 /* utils */ = {
			isa = PBXGroup;
			children = (
				BF8CFE4A2D756AC800AA5C47 /* ofConstants.h */,
				BF8CFE4B2D756AC800AA5C47 /* ofFileUtils.cpp */,
				BF8CFE4C2D756AC800AA5C47 /* ofFileUtils.h */,
				BF8CFE4D2D756AC800AA5C47 /* ofFpsCounter.cpp */,
				BF8CFE4E2D756AC800AA5C47 /* ofFpsCounter.h */,
				BF8CFE4F2D756AC800AA5C47 /* ofLog.cpp */,
				BF8CFE502D756AC800AA5C47 /* ofLog.h */,
				BF8CFE512D756AC800AA5C47 /* ofMatrixStack.cpp */,
				BF8CFE522D756AC800AA5C47 /* ofMatrixStack.h */,
				BF8CFE532D756AC800AA5C47 /* ofNoise.h */,
				BF8CFE542D756AC800AA5C47 /* ofSystemUtils.cpp */,
				BF8CFE552D756AC800AA5C47 /* ofSystemUtils.h */,
				BF8CFE562D756AC800AA5C47 /* ofThread.cpp */,
				BF8CFE572D756AC800AA5C47 /* ofThread.h */,
				BF8CFE582D756AC800AA5C47 /* ofThreadChannel.h */,
				BF8CFE592D756AC800AA5C47 /* ofTimer.cpp */,
				BF8CFE5A2D756AC800AA5C47 /* ofTimer.h */,
				BF8CFE5B2D756AC800AA5C47 /* ofTimerFps.cpp */,
				BF8CFE5C2D756AC800AA5C47 /* ofTimerFps.h */,
				BF8CFE5D2D756AC800AA5C47 /* ofURLFileLoader.cpp */,
				BF8CFE5E2D756AC800AA5C47 /* ofURLFileLoader.h */,
				BF8CFE5F2D756AC800AA5C47 /* ofUtils.cpp */,
				BF8CFE602D756AC800AA5C47 /* ofUtils.h */,
				BF8CFE612D756AC800AA5C47 /* ofXml.cpp */,
				BF8CFE622D756AC800AA5C47 /* ofXml.h */,
			);
			name = utils;
			path = ../../../libs/openFrameworks/utils;
			sourceTree = "<group>";
		};
		BF8CFE682D756AC800AA5C47 /* video */ = {
			isa = PBXGroup;
			children = (
				BF8CFE642D756AC800AA5C47 /* ofVideoGrabber.cpp */,
				BF8CFE652D756AC800AA5C47 /* ofVideoGrabber.h */,
				BF8CFE662D756AC800AA5C47 /* ofVideoPlayer.cpp */,
				BF8CFE672D756AC800AA5C47 /* ofVideoPlayer.h */,
			);
			name = video;
			path = ../../../libs/openFrameworks/video;
			sourceTree = "<group>";
		};
		BF8CFF572D758AD200AA5C47 /* app */ = {
			isa = PBXGroup;
			children = (
				BF8CFF542D758AD200AA5C47 /* ofAppiOSWindow.h */,
				BF8CFF552D758AD200AA5C47 /* ofAppiOSWindow.mm */,
				BF8CFF562D758AD200AA5C47 /* ofxiOSApp.h */,
			);
			path = app;
			sourceTree = "<group>";
		};
		BF8CFF622D758AD200AA5C47 /* core */ = {
			isa = PBXGroup;
			children = (
				BF8CFF582D758AD200AA5C47 /* ofxiOSAppDelegate.h */,
				BF8CFF592D758AD200AA5C47 /* ofxiOSAppDelegate.mm */,
				BF8CFF5A2D758AD200AA5C47 /* ofxiOSEAGLView.h */,
				BF8CFF5B2D758AD200AA5C47 /* ofxiOSEAGLView.mm */,
				BF8CFF5C2D758AD200AA5C47 /* ofxiOSGLKView.h */,
				BF8CFF5D2D758AD200AA5C47 /* ofxiOSGLKView.mm */,
				BF8CFF5E2D758AD200AA5C47 /* ofxiOSGLKViewController.h */,
				BF8CFF5F2D758AD200AA5C47 /* ofxiOSGLKViewController.mm */,
				BF8CFF602D758AD200AA5C47 /* ofxiOSViewController.h */,
				BF8CFF612D758AD200AA5C47 /* ofxiOSViewController.mm */,
			);
			path = core;
			sourceTree = "<group>";
		};
		BF8CFF662D758AD200AA5C47 /* events */ = {
			isa = PBXGroup;
			children = (
				BF8CFF632D758AD200AA5C47 /* ofxiOSAlerts.h */,
				BF8CFF642D758AD200AA5C47 /* ofxiOSAlerts.mm */,
				BF8CFF652D758AD200AA5C47 /* ofxiOSAlertsListener.h */,
			);
			path = events;
			sourceTree = "<group>";
		};
		BF8CFF722D758AD200AA5C47 /* gl */ = {
			isa = PBXGroup;
			children = (
				BF8CFF672D758AD200AA5C47 /* EAGLKView.h */,
				BF8CFF682D758AD200AA5C47 /* EAGLKView.m */,
				BF8CFF692D758AD200AA5C47 /* EAGLView.h */,
				BF8CFF6A2D758AD200AA5C47 /* EAGLView.m */,
				BF8CFF6B2D758AD200AA5C47 /* ES1Renderer.h */,
				BF8CFF6C2D758AD200AA5C47 /* ES1Renderer.m */,
				BF8CFF6D2D758AD200AA5C47 /* ES2Renderer.h */,
				BF8CFF6E2D758AD200AA5C47 /* ES2Renderer.m */,
				BF8CFF6F2D758AD200AA5C47 /* ES3Renderer.h */,
				BF8CFF702D758AD200AA5C47 /* ES3Renderer.m */,
				BF8CFF712D758AD200AA5C47 /* ESRenderer.h */,
			);
			path = gl;
			sourceTree = "<group>";
		};
		BF8CFF852D758AD200AA5C47 /* sound */ = {
			isa = PBXGroup;
			children = (
				BF8CFF732D758AD200AA5C47 /* AVSoundPlayer.h */,
				BF8CFF742D758AD200AA5C47 /* AVSoundPlayer.m */,
				BF8CFF752D758AD200AA5C47 /* ofxiOSSoundPlayer.h */,
				BF8CFF762D758AD200AA5C47 /* ofxiOSSoundPlayer.mm */,
				BF8CFF772D758AD200AA5C47 /* ofxiOSSoundStream.h */,
				BF8CFF782D758AD200AA5C47 /* ofxiOSSoundStream.mm */,
				BF8CFF792D758AD200AA5C47 /* ofxiOSSoundStreamDelegate.h */,
				BF8CFF7A2D758AD200AA5C47 /* ofxiOSSoundStreamDelegate.mm */,
				BF8CFF7B2D758AD200AA5C47 /* ofxOpenALSoundPlayer.h */,
				BF8CFF7C2D758AD200AA5C47 /* ofxOpenALSoundPlayer.cpp */,
				BF8CFF7D2D758AD200AA5C47 /* SoundEngine.h */,
				BF8CFF7E2D758AD200AA5C47 /* SoundEngine.cpp */,
				BF8CFF7F2D758AD200AA5C47 /* SoundInputStream.h */,
				BF8CFF802D758AD200AA5C47 /* SoundInputStream.m */,
				BF8CFF812D758AD200AA5C47 /* SoundOutputStream.h */,
				BF8CFF822D758AD200AA5C47 /* SoundOutputStream.m */,
				BF8CFF832D758AD200AA5C47 /* SoundStream.h */,
				BF8CFF842D758AD200AA5C47 /* SoundStream.m */,
			);
			path = sound;
			sourceTree = "<group>";
		};
		BF8CFF8E2D758AD200AA5C47 /* tvOS */ = {
			isa = PBXGroup;
			children = (
				BF8CFF862D758AD200AA5C47 /* ofxtvOSAppDelegate.h */,
				BF8CFF872D758AD200AA5C47 /* ofxtvOSAppDelegate.mm */,
				BF8CFF882D758AD200AA5C47 /* ofxtvOSGLKViewController.h */,
				BF8CFF892D758AD200AA5C47 /* ofxtvOSGLKViewController.mm */,
				BF8CFF8A2D758AD200AA5C47 /* ofxtvOSURLFileLoader.h */,
				BF8CFF8B2D758AD200AA5C47 /* ofxtvOSURLFileLoader.cpp */,
				BF8CFF8C2D758AD200AA5C47 /* ofxtvOSViewController.h */,
				BF8CFF8D2D758AD200AA5C47 /* ofxtvOSViewController.mm */,
			);
			path = tvOS;
			sourceTree = "<group>";
		};
		BF8CFFA52D758AD200AA5C47 /* utils */ = {
			isa = PBXGroup;
			children = (
				BF8CFF8F2D758AD200AA5C47 /* ofxiOSCoreHaptics.h */,
				BF8CFF902D758AD200AA5C47 /* ofxiOSCoreHaptics.mm */,
				BF8CFF912D758AD200AA5C47 /* ofxiOSCoreLocation.h */,
				BF8CFF922D758AD200AA5C47 /* ofxiOSCoreLocation.mm */,
				BF8CFF932D758AD200AA5C47 /* ofxiOSCoreMotion.h */,
				BF8CFF942D758AD200AA5C47 /* ofxiOSCoreMotion.mm */,
				BF8CFF952D758AD200AA5C47 /* ofxiOSEventAdapter.h */,
				BF8CFF962D758AD200AA5C47 /* ofxiOSEventAdapter.mm */,
				BF8CFF972D758AD200AA5C47 /* ofxiOSExternalDisplay.h */,
				BF8CFF982D758AD200AA5C47 /* ofxiOSExternalDisplay.mm */,
				BF8CFF992D758AD200AA5C47 /* ofxiOSExtras.h */,
				BF8CFF9A2D758AD200AA5C47 /* ofxiOSExtras.mm */,
				BF8CFF9B2D758AD200AA5C47 /* ofxiOSImagePicker.h */,
				BF8CFF9C2D758AD200AA5C47 /* ofxiOSImagePicker.mm */,
				BF8CFF9D2D758AD200AA5C47 /* ofxiOSKeyboard.h */,
				BF8CFF9E2D758AD200AA5C47 /* ofxiOSKeyboard.mm */,
				BF8CFF9F2D758AD200AA5C47 /* ofxiOSMapKit.h */,
				BF8CFFA02D758AD200AA5C47 /* ofxiOSMapKit.mm */,
				BF8CFFA12D758AD200AA5C47 /* ofxiOSMapKitDelegate.h */,
				BF8CFFA22D758AD200AA5C47 /* ofxiOSMapKitDelegate.mm */,
				BF8CFFA32D758AD200AA5C47 /* ofxiOSMapKitListener.h */,
				BF8CFFA42D758AD200AA5C47 /* ofxiPhoneExtras.h */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		BF8CFFAE2D758AD200AA5C47 /* video */ = {
			isa = PBXGroup;
			children = (
				BF8CFFA62D758AD200AA5C47 /* AVFoundationVideoGrabber.h */,
				BF8CFFA72D758AD200AA5C47 /* AVFoundationVideoGrabber.mm */,
				BF8CFFA82D758AD200AA5C47 /* AVFoundationVideoPlayer.h */,
				BF8CFFA92D758AD200AA5C47 /* AVFoundationVideoPlayer.m */,
				BF8CFFAA2D758AD200AA5C47 /* ofxiOSVideoGrabber.h */,
				BF8CFFAB2D758AD200AA5C47 /* ofxiOSVideoGrabber.mm */,
				BF8CFFAC2D758AD200AA5C47 /* ofxiOSVideoPlayer.h */,
				BF8CFFAD2D758AD200AA5C47 /* ofxiOSVideoPlayer.mm */,
			);
			path = video;
			sourceTree = "<group>";
		};
		BF8CFFB42D758AD200AA5C47 /* src */ = {
			isa = PBXGroup;
			children = (
				BF8CFF572D758AD200AA5C47 /* app */,
				BF8CFF622D758AD200AA5C47 /* core */,
				BF8CFF662D758AD200AA5C47 /* events */,
				BF8CFF722D758AD200AA5C47 /* gl */,
				BF8CFF852D758AD200AA5C47 /* sound */,
				BF8CFF8E2D758AD200AA5C47 /* tvOS */,
				BF8CFFA52D758AD200AA5C47 /* utils */,
				BF8CFFAE2D758AD200AA5C47 /* video */,
				BF8CFFAF2D758AD200AA5C47 /* ofxiOS.h */,
				BF8CFFB02D758AD200AA5C47 /* ofxiOSConstants.h */,
				BF8CFFB12D758AD200AA5C47 /* ofxiOSExtensions.h */,
				BF8CFFB22D758AD200AA5C47 /* ofxiPhone.h */,
				BF8CFFB32D758AD200AA5C47 /* ofxtvOS.h */,
			);
			path = src;
			sourceTree = "<group>";
		};
		BF8CFFB52D758AD200AA5C47 /* ofxiOS */ = {
			isa = PBXGroup;
			children = (
				BF8CFFB42D758AD200AA5C47 /* src */,
			);
			name = ofxiOS;
			path = ../../../addons/ofxiOS;
			sourceTree = "<group>";
		};
		"C1FDFD75-7745-48E9-9247-8E50161375B0" /* libs */ = {
			isa = PBXGroup;
			children = (
				"C98D8736-48C8-4438-9C02-A030D0587606" /* fontstash */,
			);
			path = libs;
			sourceTree = "<group>";
		};
		"C98D8736-48C8-4438-9C02-A030D0587606" /* fontstash */ = {
			isa = PBXGroup;
			children = (
				"1FB7C63F-1058-4B5D-B654-8782AB29F8B3" /* src */,
			);
			path = fontstash;
			sourceTree = "<group>";
		};
		"D2BD6EE2-4867-42F4-83A2-E9F25D2A83A3" /* src */ = {
			isa = PBXGroup;
			children = (
				"A5547EE2-32EA-4E4D-901F-743837DA224A" /* common */,
				"E98F72EC-51F9-48BB-8E33-FA1157ABB53C" /* iOS */,
			);
			path = src;
			sourceTree = "<group>";
		};
		E4D8936A11527B74007E1F53 /* src */ = {
			isa = PBXGroup;
			children = (
				E4D8936B11527B74007E1F53 /* main.mm */,
				E4D8936D11527B74007E1F53 /* ofApp.mm */,
				E4D8936C11527B74007E1F53 /* ofApp.h */,
			);
			path = src;
			sourceTree = SOURCE_ROOT;
		};
		"E98F72EC-51F9-48BB-8E33-FA1157ABB53C" /* iOS */ = {
			isa = PBXGroup;
			children = (
				"98B44F2E-E00A-4D0B-A3A0-CF787831AB58" /* ofxGPSImpliOS.h */,
				"F498E0D6-E16D-403D-90C6-3FC4D686D6B0" /* ofxGPSImpliOS.mm */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1D6058900D05DD3D006BFB54 /* BoomSugar */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "BoomSugar" */;
			buildPhases = (
				BF46A0162D754E9E00E4F02F /* ShellScript */,
				1D60588D0D05DD3D006BFB54 /* Resources */,
				1D60588E0D05DD3D006BFB54 /* Sources */,
				1D60588F0D05DD3D006BFB54 /* Frameworks */,
				9255DD331112741900D6945E /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = BoomSugar;
			productName = iPhone;
			productReference = 1D6058910D05DD3D006BFB54 /* BoomSugar.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0600;
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "BoomSugar" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* CustomTemplate */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1D6058900D05DD3D006BFB54 /* BoomSugar */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D60588D0D05DD3D006BFB54 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9936F6101BFA4DEE00891288 /* Images.xcassets in Resources */,
				9936F6121BFA65F100891288 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		9255DD331112741900D6945E /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "rsync -avz --exclude='.DS_Store' \"${SRCROOT}/bin/data/\" \"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}\"\n";
		};
		BF46A0162D754E9E00E4F02F /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/sh\n\necho \"Running: Platform: $PLATFORM_NAME\"\nif [[ \"${PLATFORM_NAME}\" == \"iphonesimulator\" ]]; then\n    OF_CORE_SDK=\"iphonesimulator\"\n    if [[ \"$HOST_ARCH\" == \"arm64\" ]]; then\n        ARCHS=\"-arch arm64\"\n    elif [[ \"$HOST_ARCH\" == \"x86_64\" ]]; then\n        ARCHS=\"-arch x86_64 arm64\"\n    else\n        echo \"Warning: Unknown host architecture '$HOST_ARCH', defaulting to arm64 for simulator\"\n        ARCHS=\"-arch arm64\"\n    fi\nelse\n    OF_CORE_SDK=\"iphoneos\"\n    ARCHS=\"-arch arm64\"\nfi\n\nexport OF_CORE_BUILD_COMMAND=\"xcodebuild -project $OF_PATH/libs/openFrameworksCompiled/project/ios/iOS+OFLib.xcodeproj \\\n-target openFrameworks -configuration ${CONFIGURATION} \\\nSDK=${OF_CORE_SDK} -sdk ${OF_CORE_SDK} ${ARCHS} \\\nCLANG_CXX_LANGUAGE_STANDARD=$CLANG_CXX_LANGUAGE_STANDARD \\\nIPHONEOS_DEPLOYMENT_TARGET=$IPHONEOS_DEPLOYMENT_TARGET \\\nGCC_PREPROCESSOR_DEFINITIONS=$USER_PREPROCESSOR_DEFINITIONS\"\n\necho \"Running: $OF_CORE_BUILD_COMMAND\"\n\n# Execute the build command\neval $OF_CORE_BUILD_COMMAND\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D60588E0D05DD3D006BFB54 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E4D8936E11527B74007E1F53 /* main.mm in Sources */,
				E4D8936F11527B74007E1F53 /* ofApp.mm in Sources */,
				"FF105C75-6C47-490C-9193-B20D01890E2C" /* ofxFontStash.cpp in Sources */,
				"DA8215FD-BA3C-4DF2-8742-B11ADB454EC8" /* ofxGPSImpliOS.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1D6058940D05DD3E006BFB54 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_LINK_OBJC_RUNTIME = YES;
				DEVELOPMENT_TEAM = FNCTZ95AC2;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = ofxiOS_Prefix.pch;
				"GCC_WARN_64_TO_32_BIT_CONVERSION[arch=*64]" = NO;
				HEADER_SEARCH_PATHS = (
					"$(OF_CORE_HEADERS)",
					src,
					src,
					../../../addons/ofxFontStash3/libs,
					../../../addons/ofxFontStash3/libs/fontstash,
					../../../addons/ofxFontStash3/libs/fontstash/src,
					../../../addons/ofxFontStash3/src,
					../../../addons/ofxGPS/src,
					../../../addons/ofxGPS/src/Android,
					../../../addons/ofxGPS/src/common,
					../../../addons/ofxGPS/src/iOS,
				);
				INFOPLIST_FILE = "ofxiOS-Info.plist";
				OTHER_LDFLAGS = (
					"$(OF_CORE_LIBS)",
					"$(OF_CORE_FRAMEWORKS)",
					"$(LIB_OF_DEBUG)",
				);
				PRODUCT_NAME = "${TARGET_NAME}";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "$(ARCHS_STANDARD)";
			};
			name = Debug;
		};
		1D6058950D05DD3E006BFB54 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_LINK_OBJC_RUNTIME = YES;
				DEVELOPMENT_TEAM = FNCTZ95AC2;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = ofxiOS_Prefix.pch;
				"GCC_WARN_64_TO_32_BIT_CONVERSION[arch=*64]" = NO;
				HEADER_SEARCH_PATHS = (
					"$(OF_CORE_HEADERS)",
					src,
					src,
					../../../addons/ofxFontStash3/libs,
					../../../addons/ofxFontStash3/libs/fontstash,
					../../../addons/ofxFontStash3/libs/fontstash/src,
					../../../addons/ofxFontStash3/src,
					../../../addons/ofxGPS/src,
					../../../addons/ofxGPS/src/Android,
					../../../addons/ofxGPS/src/common,
					../../../addons/ofxGPS/src/iOS,
				);
				INFOPLIST_FILE = "ofxiOS-Info.plist";
				OTHER_LDFLAGS = (
					"$(OF_CORE_LIBS)",
					"$(OF_CORE_FRAMEWORKS)",
					"$(LIB_OF_RELEASE)",
				);
				PRODUCT_NAME = "${TARGET_NAME}";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "$(ARCHS_STANDARD)";
			};
			name = Release;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E41D3ED613B38FB500A75A5D /* Project.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				CLANG_CXX_LIBRARY = "libc++";
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphonesimulator*]" = "";
				COMPRESS_PNG_FILES = NO;
				GCC_C_LANGUAGE_STANDARD = c17;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				"GCC_WARN_64_TO_32_BIT_CONVERSION[arch=*64]" = NO;
				GCC_WARN_ABOUT_INVALID_OFFSETOF_MACRO = NO;
				GCC_WARN_ABOUT_POINTER_SIGNEDNESS = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_ALLOW_INCOMPLETE_PROTOCOL = NO;
				GCC_WARN_PROTOTYPE_CONVERSION = NO;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = 1;
				VALID_ARCHS = "$(ARCHS_STANDARD)";
				WARNING_LDFLAGS = "-no_arch_warnings";
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E41D3ED613B38FB500A75A5D /* Project.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				CLANG_CXX_LIBRARY = "libc++";
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphonesimulator*]" = "";
				COMPRESS_PNG_FILES = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_THUMB_SUPPORT = NO;
				"GCC_WARN_64_TO_32_BIT_CONVERSION[arch=*64]" = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = 1;
				VALID_ARCHS = "$(ARCHS_STANDARD)";
				WARNING_LDFLAGS = "-no_arch_warnings";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "BoomSugar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D6058940D05DD3E006BFB54 /* Debug */,
				1D6058950D05DD3E006BFB54 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "BoomSugar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247B /* Debug */,
				C01FCF5008A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
