# GPS位置显示应用

本项目是一个基于openFrameworks的iOS应用，使用ofxGPS获取实时位置信息并显示中文界面。

## 功能特性
- 实时GPS定位
- 中文界面显示
- 显示纬度、经度、海拔、精度信息
- 触摸刷新位置
- 双击重启GPS服务

## 字体说明
应用使用微软雅黑粗体(msyhbd.ttf)来显示中文文字。
字体文件位置：bin/data/fonts/msyhbd.ttf
如果字体加载失败，会自动回退到系统默认字体。

## 位置权限
请确保在iOS设置中允许应用访问位置信息：
设置 > 隐私与安全性 > 定位服务 > BoomSugar > 使用App期间

## 使用方法
1. 启动应用后会自动开始GPS定位
2. 触摸屏幕可以刷新位置信息
3. 双击屏幕可以重启GPS服务
4. 首次定位可能需要几分钟时间

## 技术栈
- openFrameworks 0.12.1
- ofxGPS (GPS定位)
- ofTrueTypeFont (文字渲染)
