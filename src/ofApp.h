#pragma once

#include "ofxiOS.h"
#include "ofxGPS.h"

class ofApp : public ofxiOSApp {

    public:
        void setup() override;
        void update() override;
        void draw() override;
        void exit() override;

        void touchDown(ofTouchEventArgs & touch) override;
        void touchMoved(ofTouchEventArgs & touch) override;
        void touchUp(ofTouchEventArgs & touch) override;
        void touchDoubleTap(ofTouchEventArgs & touch) override;
        void touchCancelled(ofTouchEventArgs & touch) override;

        void lostFocus() override;
        void gotFocus() override;
        void gotMemoryWarning() override;
        void deviceOrientationChanged(int newOrientation) override;
        void launchedWithURL(std::string url) override;

        // GPS相关
        bool gpsEnabled;

        // 字体相关
        ofTrueTypeFont font;

        // 位置信息
        double latitude;
        double longitude;
        double altitude;
        double accuracy;
        string locationStatus;

        // GPS事件处理
        void onLocationData(const ofxGPS::LocationData & locationData);

        // UI相关
        void drawLocationInfo();

};
