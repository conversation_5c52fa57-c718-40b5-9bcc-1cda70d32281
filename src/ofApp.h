#pragma once

#include "ofMain.h"
#include "ofxGPS.h"

#ifdef TARGET_OF_IOS
#include "ofxiOS.h"
class ofApp : public ofxiOSApp {
#else
class ofApp : public ofBaseApp {
#endif

    public:
        void setup() override;
        void update() override;
        void draw() override;
        void exit() override;

#ifdef TARGET_OF_IOS
        void touchDown(ofTouchEventArgs & touch) override;
        void touchMoved(ofTouchEventArgs & touch) override;
        void touchUp(ofTouchEventArgs & touch) override;
        void touchDoubleTap(ofTouchEventArgs & touch) override;
        void touchCancelled(ofTouchEventArgs & touch) override;

        void lostFocus() override;
        void gotFocus() override;
        void gotMemoryWarning() override;
        void deviceOrientationChanged(int newOrientation) override;
        void launchedWithURL(std::string url) override;
#else
        void keyPressed(int key) override;
        void keyReleased(int key) override;
        void mouseMoved(int x, int y) override;
        void mouseDragged(int x, int y, int button) override;
        void mousePressed(int x, int y, int button) override;
        void mouseReleased(int x, int y, int button) override;
        void mouseEntered(int x, int y) override;
        void mouseExited(int x, int y) override;
        void windowResized(int w, int h) override;
        void dragEvent(ofDragInfo dragInfo) override;
        void gotMessage(ofMessage msg) override;
#endif

        // GPS相关
        bool gpsEnabled;

        // 字体相关
        ofTrueTypeFont font;

        // 位置信息
        double latitude;
        double longitude;
        double altitude;
        double accuracy;
        string locationStatus;

        // GPS事件处理
        void onLocationData(const ofxGPS::LocationData & locationData);

        // UI相关
        void drawLocationInfo();

};
