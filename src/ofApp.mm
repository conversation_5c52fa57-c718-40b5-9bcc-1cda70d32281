#include "ofApp.h"
#include "fontstash.h"

//--------------------------------------------------------------
void ofApp::setup(){
    // 初始化变量
    gpsEnabled = false;
    latitude = 0.0;
    longitude = 0.0;
    altitude = 0.0;
    accuracy = 0.0;
    locationStatus = "正在获取位置信息...";

    // 设置背景色
    ofBackground(30, 30, 30);

    // 初始化字体
    // 尝试加载字体文件，如果失败则使用bitmap字体
    bool fontLoaded = font.load("data/fonts/verdana.ttf", 24, true, true);

    if(!fontLoaded) {
        // 如果没有字体文件，将使用ofDrawBitmapString
        ofLogNotice("Font") << "字体文件未找到，将使用系统默认字体";
    } else {
        ofLogNotice("Font") << "字体加载成功";
    }

    // 初始化GPS
    gps.setup();
    if(gps.startLocationManager()) {
        gpsEnabled = true;
        locationStatus = "GPS已启动，正在定位...";
        ofLogNotice("GPS") << "GPS定位服务已启动";
    } else {
        locationStatus = "GPS启动失败，请检查权限设置";
        ofLogError("GPS") << "GPS定位服务启动失败";
    }
}

//--------------------------------------------------------------
void ofApp::update(){
    if(gpsEnabled && gps.isLocationManagerRunning()) {
        // 更新GPS数据
        if(gps.hasNewLocation()) {
            latitude = gps.getLatitude();
            longitude = gps.getLongitude();
            altitude = gps.getAltitude();
            accuracy = gps.getAccuracy();

            locationStatus = "定位成功";
            ofLogNotice("GPS") << "位置更新: " << latitude << ", " << longitude;
        }
    }
}

//--------------------------------------------------------------
void ofApp::draw(){
    drawLocationInfo();
}

//--------------------------------------------------------------
void ofApp::exit(){
    if(gpsEnabled) {
        gps.stopLocationManager();
    }
}

//--------------------------------------------------------------
void ofApp::drawLocationInfo(){
    float x = 50;
    float y = 100;
    float lineHeight = 40;

    // 标题
    ofSetColor(100, 200, 255);
    if(font.isLoaded()) {
        font.drawString("实时位置信息", x, y);
    } else {
        ofDrawBitmapString("实时位置信息", x, y);
    }

    y += lineHeight * 1.5;
    ofSetColor(255, 255, 255);

    // 状态信息
    if(font.isLoaded()) {
        font.drawString("状态: " + locationStatus, x, y);
    } else {
        ofDrawBitmapString("状态: " + locationStatus, x, y);
    }
    y += lineHeight;

    if(gpsEnabled && latitude != 0.0 && longitude != 0.0) {
        // 纬度
        string latStr = "纬度: " + ofToString(latitude, 6) + "°";
        if(font.isLoaded()) {
            font.drawString(latStr, x, y);
        } else {
            ofDrawBitmapString(latStr, x, y);
        }
        y += lineHeight;

        // 经度
        string lonStr = "经度: " + ofToString(longitude, 6) + "°";
        if(font.isLoaded()) {
            font.drawString(lonStr, x, y);
        } else {
            ofDrawBitmapString(lonStr, x, y);
        }
        y += lineHeight;

        // 海拔
        string altStr = "海拔: " + ofToString(altitude, 2) + " 米";
        if(font.isLoaded()) {
            font.drawString(altStr, x, y);
        } else {
            ofDrawBitmapString(altStr, x, y);
        }
        y += lineHeight;

        // 精度
        string accStr = "精度: ±" + ofToString(accuracy, 2) + " 米";
        if(font.isLoaded()) {
            font.drawString(accStr, x, y);
        } else {
            ofDrawBitmapString(accStr, x, y);
        }
        y += lineHeight;

        // 坐标系统信息
        y += lineHeight * 0.5;
        ofSetColor(200, 200, 200);
        if(font.isLoaded()) {
            font.drawString("坐标系统: WGS84", x, y);
        } else {
            ofDrawBitmapString("坐标系统: WGS84", x, y);
        }
        y += lineHeight * 0.8;

        // 格式化的坐标显示
        string coordStr = "坐标: " + ofToString(latitude, 6) + ", " + ofToString(longitude, 6);
        if(font.isLoaded()) {
            font.drawString(coordStr, x, y);
        } else {
            ofDrawBitmapString(coordStr, x, y);
        }

    } else {
        ofSetColor(255, 200, 100);
        if(font.isLoaded()) {
            font.drawString("等待GPS信号...", x, y);
        } else {
            ofDrawBitmapString("等待GPS信号...", x, y);
        }
    }

    // 底部提示信息
    y = ofGetHeight() - 100;
    ofSetColor(150, 150, 150);
    if(font.isLoaded()) {
        font.drawString("请确保已允许应用访问位置信息", x, y);
        y += 30;
        font.drawString("首次定位可能需要几分钟时间", x, y);
    } else {
        ofDrawBitmapString("请确保已允许应用访问位置信息", x, y);
        y += 30;
        ofDrawBitmapString("首次定位可能需要几分钟时间", x, y);
    }
}

//--------------------------------------------------------------
void ofApp::touchDown(ofTouchEventArgs & touch){
    // 触摸屏幕刷新GPS
    if(gpsEnabled) {
        locationStatus = "正在刷新位置...";
        ofLogNotice("GPS") << "用户触摸刷新GPS";
    }
}

//--------------------------------------------------------------
void ofApp::touchMoved(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::touchUp(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::touchDoubleTap(ofTouchEventArgs & touch){
    // 双击重启GPS服务
    if(gpsEnabled) {
        gps.stopLocationManager();
        if(gps.startLocationManager()) {
            locationStatus = "GPS服务已重启";
            ofLogNotice("GPS") << "GPS服务重启成功";
        } else {
            locationStatus = "GPS重启失败";
            ofLogError("GPS") << "GPS服务重启失败";
        }
    }
}

//--------------------------------------------------------------
void ofApp::touchCancelled(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::lostFocus(){

}

//--------------------------------------------------------------
void ofApp::gotFocus(){

}

//--------------------------------------------------------------
void ofApp::gotMemoryWarning(){

}

//--------------------------------------------------------------
void ofApp::deviceOrientationChanged(int newOrientation){

}

//--------------------------------------------------------------
void ofApp::launchedWithURL(std::string url){

}
